"use client"

import { useState, useEffect, useCallback } from "react"
import { useAuth } from "@/lib/hooks/use-auth"
import {
  NotificationService,
  type UserNotification,
  type NotificationPreferences
} from "@/lib/notification-service"
import {
  collection,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  type Unsubscribe
} from "firebase/firestore"
import { db } from "@/lib/firebase"

export interface UseNotificationsReturn {
  notifications: UserNotification[]
  unreadCount: number
  preferences: NotificationPreferences | null
  loading: boolean
  error: string | null
  markAsRead: (notificationId: string) => Promise<void>
  markAsClicked: (notificationId: string) => Promise<void>
  updatePreferences: (preferences: Partial<NotificationPreferences>) => Promise<void>
  initializePushNotifications: () => Promise<boolean>
  refreshNotifications: () => void
}

/**
 * Hook pour gérer les notifications d'un utilisateur
 */
export function useNotifications(): UseNotificationsReturn {
  const { user } = useAuth()
  const [notifications, setNotifications] = useState<UserNotification[]>([])
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [unsubscribe, setUnsubscribe] = useState<Unsubscribe | null>(null)

  // Calculer le nombre de notifications non lues
  const unreadCount = notifications.filter(notification => !notification.isRead).length

  /**
   * Initialise les notifications push pour l'utilisateur
   */
  const initializePushNotifications = useCallback(async (): Promise<boolean> => {
    if (!user?.uid) return false

    try {
      const token = await NotificationService.initializeForUser(user.uid)
      if (token) {
        // Écouter les messages en temps réel
        const unsubscribeMessages = NotificationService.onMessage((payload) => {
          console.log("Message reçu:", payload)

          // Afficher une notification dans l'app si elle est ouverte
          if (payload.notification) {
            // Vous pouvez utiliser une bibliothèque comme react-hot-toast ici
            console.log("Nouvelle notification:", payload.notification.title)
          }

          // Rafraîchir la liste des notifications
          refreshNotifications()
        })

        return true
      }
      return false
    } catch (error) {
      console.error("Erreur lors de l'initialisation des notifications push:", error)
      setError("Impossible d'initialiser les notifications push")
      return false
    }
  }, [user?.uid])

  /**
   * Charge les notifications de l'utilisateur
   */
  const loadNotifications = useCallback(async () => {
    if (!user?.uid) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      // Écouter les notifications en temps réel
      const notificationsQuery = query(
        collection(db(), "userNotifications"),
        where("userId", "==", user.uid),
        orderBy("createdAt", "desc"),
        limit(50)
      )

      const unsubscribeNotifications = onSnapshot(
        notificationsQuery,
        (snapshot) => {
          const notificationsList = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          } as UserNotification))

          setNotifications(notificationsList)
          setLoading(false)
        },
        (error) => {
          console.error("Erreur lors du chargement des notifications:", error)
          setError("Impossible de charger les notifications")
          setLoading(false)
        }
      )

      setUnsubscribe(unsubscribeNotifications)
    } catch (error) {
      console.error("Erreur lors du chargement des notifications:", error)
      setError("Impossible de charger les notifications")
      setLoading(false)
    }
  }, [user?.uid])

  /**
   * Charge les préférences de l'utilisateur
   */
  const loadPreferences = useCallback(async () => {
    if (!user?.uid) return

    try {
      const userPreferences = await NotificationService.getUserPreferences(user.uid)
      setPreferences(userPreferences)
    } catch (error) {
      console.error("Erreur lors du chargement des préférences:", error)
    }
  }, [user?.uid])

  /**
   * Marque une notification comme lue
   */
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await NotificationService.markAsRead(notificationId)
    } catch (error) {
      console.error("Erreur lors du marquage comme lu:", error)
      setError("Impossible de marquer la notification comme lue")
    }
  }, [])

  /**
   * Marque une notification comme cliquée
   */
  const markAsClicked = useCallback(async (notificationId: string) => {
    try {
      await NotificationService.markAsClicked(notificationId)
    } catch (error) {
      console.error("Erreur lors du marquage comme cliqué:", error)
      setError("Impossible de marquer la notification comme cliquée")
    }
  }, [])

  /**
   * Met à jour les préférences de l'utilisateur
   */
  const updatePreferences = useCallback(async (newPreferences: Partial<NotificationPreferences>) => {
    if (!user?.uid) return

    try {
      await NotificationService.updateUserPreferences(user.uid, newPreferences)
      setPreferences(prev => prev ? { ...prev, ...newPreferences } : null)
    } catch (error) {
      console.error("Erreur lors de la mise à jour des préférences:", error)
      setError("Impossible de mettre à jour les préférences")
    }
  }, [user?.uid])

  /**
   * Rafraîchit manuellement les notifications
   */
  const refreshNotifications = useCallback(() => {
    loadNotifications()
  }, [loadNotifications])

  // Charger les données au montage du composant avec délai pour ne pas bloquer
  useEffect(() => {
    if (user?.uid) {
      // Délai pour permettre au dashboard principal de se charger d'abord
      const timeoutId = setTimeout(() => {
        loadNotifications()
        loadPreferences()
      }, 1500) // Délai de 1.5 secondes

      return () => {
        clearTimeout(timeoutId)
        if (unsubscribe) {
          unsubscribe()
        }
      }
    } else {
      setNotifications([])
      setPreferences(null)
      setLoading(false)
    }
  }, [user?.uid])

  // Nettoyer l'abonnement quand il change
  useEffect(() => {
    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [unsubscribe])

  return {
    notifications,
    unreadCount,
    preferences,
    loading,
    error,
    markAsRead,
    markAsClicked,
    updatePreferences,
    initializePushNotifications,
    refreshNotifications
  }
}

/**
 * Hook simplifié pour obtenir uniquement le nombre de notifications non lues
 * Optimisé pour ne pas bloquer le chargement principal
 */
export function useUnreadNotificationCount(): { unreadCount: number; loading: boolean } {
  const { user } = useAuth()
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (!user?.uid) {
      setUnreadCount(0)
      setLoading(false)
      return
    }

    // Délai pour ne pas interférer avec le chargement principal
    const timeoutId = setTimeout(() => {
      const notificationsQuery = query(
        collection(db(), "userNotifications"),
        where("userId", "==", user.uid),
        where("isRead", "==", false),
        limit(50) // Limiter pour optimiser les performances
      )

      const unsubscribe = onSnapshot(
        notificationsQuery,
        (snapshot) => {
          setUnreadCount(snapshot.size)
          setLoading(false)
        },
        (error) => {
          console.error("Erreur lors du comptage des notifications non lues:", error)
          setUnreadCount(0)
          setLoading(false)
        }
      )

      return () => unsubscribe()
    }, 2000) // Délai de 2 secondes

    return () => {
      clearTimeout(timeoutId)
      setLoading(false)
    }
  }, [user?.uid])

  return { unreadCount, loading }
}
