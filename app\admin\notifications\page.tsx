"use client"

import { useState } from "react"
import { NotificationList } from "@/components/admin/notification-list"
import { NotificationForm } from "@/components/admin/notification-form"
import { NotificationAnalytics } from "@/components/admin/notification-analytics"
import { NotificationUsers } from "@/components/admin/notification-users"
import { NotificationSettingsComponent } from "@/components/admin/notification-settings"
import { NotificationTest } from "@/components/admin/notification-test"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Bell, Settings, BarChart3, Users, Send } from "lucide-react"
import { type NotificationData } from "@/lib/notification-service"
import Link from "next/link"

export default function NotificationsAdminPage() {
  const [activeTab, setActiveTab] = useState("list")
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [selectedNotification, setSelectedNotification] = useState<NotificationData | null>(null)

  const handleCreate = () => {
    setShowCreateDialog(true)
  }

  const handleEdit = (notification: NotificationData) => {
    setSelectedNotification(notification)
    setShowEditDialog(true)
  }

  const handleView = (notification: NotificationData) => {
    setSelectedNotification(notification)
    // Vous pouvez implémenter une vue détaillée ici
    console.log("Voir notification:", notification)
  }

  const handleSuccess = () => {
    setShowCreateDialog(false)
    setShowEditDialog(false)
    setSelectedNotification(null)
  }

  const handleCancel = () => {
    setShowCreateDialog(false)
    setShowEditDialog(false)
    setSelectedNotification(null)
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/admin">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Notifications</h1>
            <p className="text-muted-foreground">
              Gérez les notifications push et in-app de votre application
            </p>
          </div>
        </div>
        <Button onClick={handleCreate}>
          <Send className="h-4 w-4 mr-2" />
          Nouvelle notification
        </Button>
      </div>

      {/* Onglets */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="list" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytiques
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Utilisateurs
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Paramètres
          </TabsTrigger>
          <TabsTrigger value="test" className="flex items-center gap-2">
            <Send className="h-4 w-4" />
            Tests
          </TabsTrigger>
        </TabsList>

        {/* Liste des notifications */}
        <TabsContent value="list" className="space-y-6">
          <NotificationList
            onCreate={handleCreate}
            onEdit={handleEdit}
            onView={handleView}
          />
        </TabsContent>

        {/* Analytiques */}
        <TabsContent value="analytics" className="space-y-6">
          <NotificationAnalytics />
        </TabsContent>

        {/* Gestion des utilisateurs */}
        <TabsContent value="users" className="space-y-6">
          <NotificationUsers />
        </TabsContent>

        {/* Paramètres */}
        <TabsContent value="settings" className="space-y-6">
          <NotificationSettingsComponent />
        </TabsContent>

        {/* Tests */}
        <TabsContent value="test" className="space-y-6">
          <NotificationTest />
        </TabsContent>
      </Tabs>

      {/* Dialog de création */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Créer une nouvelle notification</DialogTitle>
          </DialogHeader>
          <NotificationForm
            mode="create"
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </DialogContent>
      </Dialog>

      {/* Dialog d'édition */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Modifier la notification</DialogTitle>
          </DialogHeader>
          <NotificationForm
            mode="edit"
            initialData={selectedNotification || undefined}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
