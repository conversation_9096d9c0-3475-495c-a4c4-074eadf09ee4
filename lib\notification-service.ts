import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  onSnapshot,
  type Unsubscribe
} from "firebase/firestore"
import { db, messaging } from "@/lib/firebase"
import { getToken, onMessage, deleteToken } from "firebase/messaging"
import { NotificationAnalyticsService } from "@/lib/notification-analytics"

// Types pour les notifications
export interface NotificationData {
  id?: string
  title: string
  body: string
  type: 'general' | 'news' | 'page' | 'system'
  targetType: 'all' | 'groups' | 'users' | 'individual'
  targetGroups?: string[]
  targetUsers?: string[]
  linkType?: 'none' | 'news' | 'page'
  linkId?: string
  linkUrl?: string
  priority: 'low' | 'normal' | 'high'
  scheduledAt?: Date
  expiresAt?: Date
  createdBy: string
  createdAt?: any
  updatedAt?: any
  status: 'draft' | 'scheduled' | 'sent' | 'failed'
  sentCount?: number
  readCount?: number
  clickCount?: number
  metadata?: Record<string, any>
}

export interface UserNotification {
  id?: string
  notificationId: string
  userId: string
  isRead: boolean
  isClicked: boolean
  readAt?: Date
  clickedAt?: Date
  createdAt?: any
}

export interface NotificationPreferences {
  userId: string
  pushEnabled: boolean
  inAppEnabled: boolean
  emailEnabled?: boolean
  categories: {
    general: boolean
    news: boolean
    pages: boolean
    system: boolean
  }
  quietHours?: {
    enabled: boolean
    start: string // HH:mm format
    end: string // HH:mm format
  }
  updatedAt?: any
}

/**
 * Service pour gérer les notifications
 */
export const NotificationService = {
  /**
   * Initialise le service de notifications pour un utilisateur
   */
  async initializeForUser(userId: string): Promise<string | null> {
    try {
      // Vérifier si nous sommes dans un navigateur
      if (typeof window === 'undefined') {
        console.warn("Service worker non disponible côté serveur")
        return null
      }

      // Enregistrer le service worker Firebase Messaging
      if ('serviceWorker' in navigator && window.location.protocol === 'https:') {
        try {
          // Vérifier si un service worker Firebase est déjà enregistré
          const existingRegistrations = await navigator.serviceWorker.getRegistrations()
          const fcmRegistration = existingRegistrations.find(reg =>
            reg.scope.includes('firebase-cloud-messaging-push-scope') ||
            reg.active?.scriptURL.includes('firebase-messaging-sw')
          )

          if (!fcmRegistration) {
            // Utiliser le service worker statique pour éviter les problèmes de sécurité
            const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
              scope: '/firebase-cloud-messaging-push-scope'
            })
            console.log('Service worker Firebase Messaging enregistré:', registration)
          } else {
            console.log('Service worker Firebase Messaging déjà enregistré')
          }
        } catch (swError) {
          console.warn('Impossible d\'enregistrer le service worker Firebase:', swError)
          // Continuer sans service worker - les notifications in-app fonctionneront toujours
        }
      } else if (window.location.protocol !== 'https:') {
        console.warn('Service workers nécessitent HTTPS en production')
      }

      // Vérifier si FCM est supporté
      let messagingInstance
      try {
        messagingInstance = messaging()
        if (!messagingInstance) {
          console.warn("Firebase Cloud Messaging non disponible")
          return null
        }
      } catch (messagingError) {
        console.warn("Erreur lors de l'initialisation de FCM:", messagingError)
        return null
      }

      // Demander la permission pour les notifications
      const permission = await Notification.requestPermission()
      if (permission !== 'granted') {
        console.warn("Permission de notification refusée")
        return null
      }

      // Obtenir le token FCM
      const vapidKey = process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY
      const tokenOptions = vapidKey ? { vapidKey } : undefined

      const token = await getToken(messagingInstance, tokenOptions)

      if (token) {
        // Sauvegarder le token dans Firestore
        await this.saveUserToken(userId, token)
        console.log("Token FCM sauvegardé:", token)

        // Écouter les messages du service worker pour les analytics
        if ('serviceWorker' in navigator) {
          navigator.serviceWorker.addEventListener('message', async (event) => {
            if (event.data?.type === 'NOTIFICATION_CLICKED' && event.data?.notificationId) {
              try {
                await NotificationAnalyticsService.updateNotificationAnalytics(
                  event.data.notificationId,
                  { clickCount: 1 }
                )
              } catch (error) {
                console.warn('Impossible de mettre à jour les analytics de clic:', error)
              }
            } else if (event.data?.type === 'NOTIFICATION_DISMISSED' && event.data?.notificationId) {
              // Optionnel: tracker les notifications fermées
              console.log('Notification fermée:', event.data.notificationId)
            }
          })
        }

        return token
      }

      return null
    } catch (error) {
      console.error("Erreur lors de l'initialisation des notifications:", error)
      return null
    }
  },

  /**
   * Sauvegarde le token FCM d'un utilisateur
   */
  async saveUserToken(userId: string, token: string): Promise<void> {
    try {
      const userTokenRef = doc(db(), "userTokens", userId)
      await updateDoc(userTokenRef, {
        fcmToken: token,
        updatedAt: serverTimestamp(),
        platform: this.getPlatform(),
        userAgent: navigator.userAgent
      }).catch(async () => {
        // Si le document n'existe pas, le créer
        await addDoc(collection(db(), "userTokens"), {
          userId,
          fcmToken: token,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          platform: this.getPlatform(),
          userAgent: navigator.userAgent
        })
      })
    } catch (error) {
      console.error("Erreur lors de la sauvegarde du token:", error)
      throw error
    }
  },

  /**
   * Supprime le token FCM d'un utilisateur
   */
  async removeUserToken(userId: string): Promise<void> {
    try {
      const messagingInstance = messaging()
      if (messagingInstance) {
        await deleteToken(messagingInstance)
      }

      const userTokenRef = doc(db(), "userTokens", userId)
      await deleteDoc(userTokenRef)
    } catch (error) {
      console.error("Erreur lors de la suppression du token:", error)
      throw error
    }
  },

  /**
   * Écoute les messages en temps réel
   */
  onMessage(callback: (payload: any) => void): Unsubscribe | null {
    try {
      const messagingInstance = messaging()
      if (!messagingInstance) return null

      return onMessage(messagingInstance, callback)
    } catch (error) {
      console.error("Erreur lors de l'écoute des messages:", error)
      return null
    }
  },

  /**
   * Crée une nouvelle notification
   */
  async createNotification(notificationData: Omit<NotificationData, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db(), "notifications"), {
        ...notificationData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        sentCount: 0,
        readCount: 0,
        clickCount: 0
      })

      // Enregistrer les analytics si activées
      try {
        await NotificationAnalyticsService.recordNotificationAnalytics(docRef.id, {
          notificationType: notificationData.type,
          targetType: notificationData.targetType,
          platform: 'web'
        })
      } catch (analyticsError) {
        console.warn("Impossible d'enregistrer les analytics:", analyticsError)
        // Ne pas faire échouer la création de notification pour les analytics
      }

      return docRef.id
    } catch (error) {
      console.error("Erreur lors de la création de la notification:", error)
      throw error
    }
  },

  /**
   * Met à jour une notification
   */
  async updateNotification(notificationId: string, updates: Partial<NotificationData>): Promise<void> {
    try {
      const notificationRef = doc(db(), "notifications", notificationId)
      await updateDoc(notificationRef, {
        ...updates,
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      console.error("Erreur lors de la mise à jour de la notification:", error)
      throw error
    }
  },

  /**
   * Supprime une notification
   */
  async deleteNotification(notificationId: string): Promise<void> {
    try {
      const notificationRef = doc(db(), "notifications", notificationId)
      await deleteDoc(notificationRef)

      // Supprimer aussi toutes les notifications utilisateur associées
      const userNotificationsQuery = query(
        collection(db(), "userNotifications"),
        where("notificationId", "==", notificationId)
      )
      const userNotificationsSnapshot = await getDocs(userNotificationsQuery)

      const deletePromises = userNotificationsSnapshot.docs.map(doc => deleteDoc(doc.ref))
      await Promise.all(deletePromises)
    } catch (error) {
      console.error("Erreur lors de la suppression de la notification:", error)
      throw error
    }
  },

  /**
   * Récupère les notifications d'un utilisateur
   */
  async getUserNotifications(userId: string, limitCount: number = 50): Promise<UserNotification[]> {
    try {
      const userNotificationsQuery = query(
        collection(db(), "userNotifications"),
        where("userId", "==", userId),
        orderBy("createdAt", "desc"),
        limit(limitCount)
      )

      const snapshot = await getDocs(userNotificationsQuery)
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as UserNotification))
    } catch (error) {
      console.error("Erreur lors de la récupération des notifications utilisateur:", error)
      throw error
    }
  },

  /**
   * Marque une notification comme lue
   */
  async markAsRead(userNotificationId: string): Promise<void> {
    try {
      const userNotificationRef = doc(db(), "userNotifications", userNotificationId)
      const userNotificationDoc = await getDoc(userNotificationRef)

      if (userNotificationDoc.exists()) {
        const data = userNotificationDoc.data()

        await updateDoc(userNotificationRef, {
          isRead: true,
          readAt: serverTimestamp()
        })

        // Mettre à jour les analytics
        try {
          await NotificationAnalyticsService.updateNotificationAnalytics(data.notificationId, {
            readCount: 1 // Sera agrégé côté analytics
          })
        } catch (analyticsError) {
          console.warn("Impossible de mettre à jour les analytics de lecture:", analyticsError)
        }
      }
    } catch (error) {
      console.error("Erreur lors du marquage comme lu:", error)
      throw error
    }
  },

  /**
   * Marque une notification comme cliquée
   */
  async markAsClicked(userNotificationId: string): Promise<void> {
    try {
      const userNotificationRef = doc(db(), "userNotifications", userNotificationId)
      const userNotificationDoc = await getDoc(userNotificationRef)

      if (userNotificationDoc.exists()) {
        const data = userNotificationDoc.data()

        await updateDoc(userNotificationRef, {
          isClicked: true,
          clickedAt: serverTimestamp()
        })

        // Mettre à jour les analytics
        try {
          await NotificationAnalyticsService.updateNotificationAnalytics(data.notificationId, {
            clickCount: 1 // Sera agrégé côté analytics
          })
        } catch (analyticsError) {
          console.warn("Impossible de mettre à jour les analytics de clic:", analyticsError)
        }
      }
    } catch (error) {
      console.error("Erreur lors du marquage comme cliqué:", error)
      throw error
    }
  },

  /**
   * Récupère les préférences de notification d'un utilisateur
   */
  async getUserPreferences(userId: string): Promise<NotificationPreferences | null> {
    try {
      const preferencesRef = doc(db(), "notificationPreferences", userId)
      const snapshot = await getDoc(preferencesRef)

      if (snapshot.exists()) {
        return snapshot.data() as NotificationPreferences
      }

      // Créer des préférences par défaut si elles n'existent pas
      const defaultPreferences: NotificationPreferences = {
        userId,
        pushEnabled: true,
        inAppEnabled: true,
        emailEnabled: false,
        categories: {
          general: true,
          news: true,
          pages: true,
          system: true
        },
        quietHours: {
          enabled: false,
          start: "22:00",
          end: "08:00"
        }
      }

      await this.updateUserPreferences(userId, defaultPreferences)
      return defaultPreferences
    } catch (error) {
      console.error("Erreur lors de la récupération des préférences:", error)
      return null
    }
  },

  /**
   * Met à jour les préférences de notification d'un utilisateur
   */
  async updateUserPreferences(userId: string, preferences: Partial<NotificationPreferences>): Promise<void> {
    try {
      const preferencesRef = doc(db(), "notificationPreferences", userId)
      await updateDoc(preferencesRef, {
        ...preferences,
        updatedAt: serverTimestamp()
      }).catch(async () => {
        // Si le document n'existe pas, le créer
        await addDoc(collection(db(), "notificationPreferences"), {
          userId,
          ...preferences,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        })
      })
    } catch (error) {
      console.error("Erreur lors de la mise à jour des préférences:", error)
      throw error
    }
  },

  /**
   * Détermine la plateforme actuelle
   */
  getPlatform(): string {
    if (typeof window === 'undefined') return 'server'

    const userAgent = navigator.userAgent.toLowerCase()
    if (userAgent.includes('android')) return 'android'
    if (userAgent.includes('iphone') || userAgent.includes('ipad')) return 'ios'
    if (userAgent.includes('windows')) return 'windows'
    if (userAgent.includes('mac')) return 'macos'
    if (userAgent.includes('linux')) return 'linux'

    return 'web'
  }
}
