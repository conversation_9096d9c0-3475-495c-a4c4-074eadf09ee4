import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  getDocs, 
  getDoc,
  query, 
  where, 
  orderBy, 
  limit, 
  serverTimestamp,
  Timestamp,
  writeBatch
} from "firebase/firestore"
import { db } from "@/lib/firebase"

// Types pour les analytics
export interface NotificationAnalytics {
  id?: string
  notificationId: string
  date: string // Format YYYY-MM-DD
  sentCount: number
  deliveredCount: number
  readCount: number
  clickCount: number
  bounceCount: number
  platform: 'web' | 'android' | 'ios' | 'all'
  targetType: 'all' | 'groups' | 'users' | 'individual'
  notificationType: 'general' | 'news' | 'page' | 'system'
  createdAt?: any
  updatedAt?: any
}

export interface AnalyticsSettings {
  enabled: boolean
  dailyLimit: number
  currentUsage: number
  lastReset: string // Date ISO string
  autoDisableOnLimit: boolean
  priorityMode: 'essential' | 'normal' | 'full'
}

export interface AnalyticsSummary {
  totalSent: number
  totalRead: number
  totalClicked: number
  readRate: number
  clickRate: number
  activeUsers: number
  topPerformingType: string
  recentTrend: 'up' | 'down' | 'stable'
  periodComparison: {
    current: number
    previous: number
    change: number
  }
}

/**
 * Service pour gérer les analytics des notifications avec optimisation Firebase
 */
export const NotificationAnalyticsService = {
  /**
   * Récupère les paramètres d'analytics
   */
  async getAnalyticsSettings(): Promise<AnalyticsSettings> {
    try {
      const settingsRef = doc(db(), "settings", "notificationAnalytics")
      const snapshot = await getDoc(settingsRef)
      
      if (snapshot.exists()) {
        return snapshot.data() as AnalyticsSettings
      }

      // Créer des paramètres par défaut
      const defaultSettings: AnalyticsSettings = {
        enabled: true,
        dailyLimit: 45000, // Laisser 5000 appels pour les autres opérations
        currentUsage: 0,
        lastReset: new Date().toISOString().split('T')[0],
        autoDisableOnLimit: true,
        priorityMode: 'normal'
      }

      await updateDoc(settingsRef, defaultSettings).catch(async () => {
        await addDoc(collection(db(), "settings"), {
          id: "notificationAnalytics",
          ...defaultSettings,
          createdAt: serverTimestamp()
        })
      })

      return defaultSettings
    } catch (error) {
      console.error("Erreur lors de la récupération des paramètres analytics:", error)
      // Retourner des paramètres par défaut en cas d'erreur
      return {
        enabled: false,
        dailyLimit: 45000,
        currentUsage: 0,
        lastReset: new Date().toISOString().split('T')[0],
        autoDisableOnLimit: true,
        priorityMode: 'essential'
      }
    }
  },

  /**
   * Met à jour les paramètres d'analytics
   */
  async updateAnalyticsSettings(settings: Partial<AnalyticsSettings>): Promise<void> {
    try {
      const settingsRef = doc(db(), "settings", "notificationAnalytics")
      await updateDoc(settingsRef, {
        ...settings,
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      console.error("Erreur lors de la mise à jour des paramètres analytics:", error)
      throw error
    }
  },

  /**
   * Vérifie si les analytics peuvent être utilisées (limite Firebase)
   */
  async canUseAnalytics(operationCost: number = 1): Promise<boolean> {
    try {
      const settings = await this.getAnalyticsSettings()
      
      if (!settings.enabled) return false

      // Vérifier si on a changé de jour
      const today = new Date().toISOString().split('T')[0]
      if (settings.lastReset !== today) {
        // Réinitialiser le compteur quotidien
        await this.updateAnalyticsSettings({
          currentUsage: 0,
          lastReset: today
        })
        return true
      }

      // Vérifier la limite
      return (settings.currentUsage + operationCost) <= settings.dailyLimit
    } catch (error) {
      console.error("Erreur lors de la vérification des limites analytics:", error)
      return false
    }
  },

  /**
   * Incrémente l'usage Firebase
   */
  async incrementUsage(operationCost: number = 1): Promise<void> {
    try {
      const settings = await this.getAnalyticsSettings()
      const newUsage = settings.currentUsage + operationCost

      await this.updateAnalyticsSettings({
        currentUsage: newUsage
      })

      // Auto-désactiver si limite atteinte
      if (settings.autoDisableOnLimit && newUsage >= settings.dailyLimit) {
        await this.updateAnalyticsSettings({
          enabled: false
        })
        console.warn("Analytics désactivées automatiquement - limite Firebase atteinte")
      }
    } catch (error) {
      console.error("Erreur lors de l'incrémentation de l'usage:", error)
    }
  },

  /**
   * Enregistre des analytics pour une notification
   */
  async recordNotificationAnalytics(
    notificationId: string,
    data: Partial<NotificationAnalytics>
  ): Promise<void> {
    const canUse = await this.canUseAnalytics(2) // Coût: 1 lecture + 1 écriture
    if (!canUse) {
      console.log("Analytics désactivées - limite Firebase")
      return
    }

    try {
      const today = new Date().toISOString().split('T')[0]
      const analyticsData: Omit<NotificationAnalytics, 'id'> = {
        notificationId,
        date: today,
        sentCount: data.sentCount || 0,
        deliveredCount: data.deliveredCount || 0,
        readCount: data.readCount || 0,
        clickCount: data.clickCount || 0,
        bounceCount: data.bounceCount || 0,
        platform: data.platform || 'web',
        targetType: data.targetType || 'all',
        notificationType: data.notificationType || 'general',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      }

      await addDoc(collection(db(), "notificationAnalytics"), analyticsData)
      await this.incrementUsage(2)
    } catch (error) {
      console.error("Erreur lors de l'enregistrement des analytics:", error)
      throw error
    }
  },

  /**
   * Met à jour les analytics d'une notification
   */
  async updateNotificationAnalytics(
    notificationId: string,
    updates: Partial<NotificationAnalytics>
  ): Promise<void> {
    const canUse = await this.canUseAnalytics(3) // Coût: 1 requête + 1 lecture + 1 écriture
    if (!canUse) return

    try {
      const analyticsQuery = query(
        collection(db(), "notificationAnalytics"),
        where("notificationId", "==", notificationId),
        where("date", "==", new Date().toISOString().split('T')[0]),
        limit(1)
      )

      const snapshot = await getDocs(analyticsQuery)
      
      if (!snapshot.empty) {
        const docRef = snapshot.docs[0].ref
        await updateDoc(docRef, {
          ...updates,
          updatedAt: serverTimestamp()
        })
      } else {
        // Créer une nouvelle entrée si elle n'existe pas
        await this.recordNotificationAnalytics(notificationId, updates)
      }

      await this.incrementUsage(3)
    } catch (error) {
      console.error("Erreur lors de la mise à jour des analytics:", error)
    }
  },

  /**
   * Récupère un résumé des analytics
   */
  async getAnalyticsSummary(days: number = 30): Promise<AnalyticsSummary> {
    const canUse = await this.canUseAnalytics(5) // Coût estimé pour les requêtes
    if (!canUse) {
      // Retourner des données par défaut si analytics désactivées
      return {
        totalSent: 0,
        totalRead: 0,
        totalClicked: 0,
        readRate: 0,
        clickRate: 0,
        activeUsers: 0,
        topPerformingType: 'general',
        recentTrend: 'stable',
        periodComparison: {
          current: 0,
          previous: 0,
          change: 0
        }
      }
    }

    try {
      const endDate = new Date()
      const startDate = new Date(endDate.getTime() - (days * 24 * 60 * 60 * 1000))
      
      const analyticsQuery = query(
        collection(db(), "notificationAnalytics"),
        where("date", ">=", startDate.toISOString().split('T')[0]),
        where("date", "<=", endDate.toISOString().split('T')[0]),
        orderBy("date", "desc")
      )

      const snapshot = await getDocs(analyticsQuery)
      const analytics = snapshot.docs.map(doc => doc.data() as NotificationAnalytics)

      // Calculer les totaux
      const totalSent = analytics.reduce((sum, a) => sum + a.sentCount, 0)
      const totalRead = analytics.reduce((sum, a) => sum + a.readCount, 0)
      const totalClicked = analytics.reduce((sum, a) => sum + a.clickCount, 0)

      // Calculer les taux
      const readRate = totalSent > 0 ? (totalRead / totalSent) * 100 : 0
      const clickRate = totalRead > 0 ? (totalClicked / totalRead) * 100 : 0

      // Trouver le type le plus performant
      const typePerformance = analytics.reduce((acc, a) => {
        if (!acc[a.notificationType]) {
          acc[a.notificationType] = { sent: 0, read: 0 }
        }
        acc[a.notificationType].sent += a.sentCount
        acc[a.notificationType].read += a.readCount
        return acc
      }, {} as Record<string, { sent: number; read: number }>)

      const topPerformingType = Object.entries(typePerformance)
        .map(([type, data]) => ({
          type,
          rate: data.sent > 0 ? data.read / data.sent : 0
        }))
        .sort((a, b) => b.rate - a.rate)[0]?.type || 'general'

      await this.incrementUsage(5)

      return {
        totalSent,
        totalRead,
        totalClicked,
        readRate: Math.round(readRate * 100) / 100,
        clickRate: Math.round(clickRate * 100) / 100,
        activeUsers: 0, // À implémenter avec une requête séparée si nécessaire
        topPerformingType,
        recentTrend: 'stable', // À calculer avec les données historiques
        periodComparison: {
          current: totalSent,
          previous: 0, // À calculer avec la période précédente
          change: 0
        }
      }
    } catch (error) {
      console.error("Erreur lors de la récupération du résumé analytics:", error)
      throw error
    }
  }
}
